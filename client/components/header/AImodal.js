import {
    Button,
    Table,
    message
} from "antd";
import {
    cadreAnalysisList,
    cadreCheck,
    exportbg,
    getCadreCheckRedisBoolean,
    getOrgRedisBoolean,
    getRedisBoolean,
    meetingMaterialsDownload,
    mockMaterialsDownloadList,
    orgAnalysisList,
    updateByCadreCheckId
} from "client/apis/cadre-portrait";
import { uuid } from "client/tool/uuid";
import * as echarts from "echarts";
import { useEffect, useState } from "react";
import EChartsReact from "./EChartsReact";
import "./header.less";
import download from "./images/download.png";
import tips from "./images/tips.png";
import UploadIcon from "./images/UploadIcon.png";
const ModalAI = (props) => {
    const { visble, handleCancel, menuKey, history } = props;
    // 进度
    const [progress, setProgress] = useState(0);
    // 实时进度
    const [newprogress, setNewprogress] = useState(0);
    // 总进度
    const [progresstotle, setProgresstotle] = useState(0);
    const [newprogresstotle, setnewProgresstotle] = useState(0);
    // 报告状态
    const [reportstatus, setReportstatus] = useState(true);
    // 生成报告状态
    const [generatereportstatus, setGeneratereportstatus] = useState(false);
    // 报告列表
    const [reportList, setReportList] = useState([]);
    // 选中的方案
    const [selectedOption, setSelectedOption] = useState([]);
    const [optionData, setOption] = useState({
        title: {
            text: "0%",
            x: "center",
            y: "40%",
            textStyle: {
                color: "#333",
                fontSize: 12,
                fontWeight: "bold",
            },
        },
        series: [
            {
                type: "gauge",
                radius: "100%",
                clockwise: true,
                // 修改 startAngle 为 90 度
                startAngle: 90,
                // 按顺时针方向递减角度
                endAngle: -270,
                splitNumber: 20,
                detail: {
                    offsetCenter: [0, -20],
                    formatter: " ",
                },
                pointer: {
                    show: false,
                },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: [
                            [0, "#1FA1FD"],
                            [
                                0 / 100,
                                new echarts.graphic.RadialGradient(
                                    1,
                                    0,
                                    1,
                                    [
                                        {
                                            offset: 0,
                                            color: "#1FA1FD",
                                        },
                                        {
                                            offset: 0.5,
                                            color: "#F2F2F2",
                                        },
                                        {
                                            offset: 1,
                                            color: "#1FA1FD",
                                        },
                                    ],
                                    true
                                ),
                            ],
                            [1, "#F2F2F2"],
                        ],
                        width: 35,
                    },
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: true,
                    length: 140,
                    lineStyle: {
                        color: "#fff",
                        width: 7,
                    },
                },
                axisLabel: {
                    show: false,
                },
            },
            {
                type: "pie",
                name: "失败：",
                radius: [0, "50.5%"],
                data: [3],
                hoverAnimation: false,
                clockWise: false,
                itemStyle: {
                    normal: {
                        color: "#fff",
                        shadowColor: "#58B6F8",
                        shadowBlur: 5,
                        shadowOffsetX: 0,
                        shadowOffsetY: 0,
                    },
                },
                label: {
                    normal: {
                        show: true,
                        position: "center",
                        padding: [20, 0, 0, 0],
                        align: "center",
                        fontSize: 10,
                        formatter: function (param) {
                            return "0/0";
                        },
                        rich: {
                            a: {
                                fontSize: 10,
                                color: "#333333",
                            },
                            b: {
                                fontSize: 10,
                                color: "#333333",
                            },
                        },
                    },
                },
            },
        ],
    });
    //表格数据
    const [tableData, setTableData] = useState([])
    // 表格总数
    const [totalData, setTotalData] = useState(0)
    // 表格分页
    const [page, setPage] = useState(1)
    const [bgstatus, setBgstatus] = useState(false)
    //加载中
    const [loading, setLoading] = useState(false)
    //总问题
    const [totalq, setTotalq] = useState(0)
    //用户数
    const [usernum, setUsernum] = useState(0)
    //是否显示重新加载
    const [reload, setReload] = useState(false)
    // 时间
    const [timedata, setTimedata] = useState(0)
    //uuid
    const [uuiddata, setUuiddata] = useState(uuid())
    // 按钮加载中
    const [btnloading, setBtnloading] = useState(false)
    // 生成报告数据
    const getprogressData = async (pageNum = 1) => {
        try {
            let progressnum = progress;
            let progresstotlenum = progresstotle;
            let newprogresstotlenum = newprogresstotle;
            let obj = {
                page: pageNum,
                page_size: 10,
            }
            if (menuKey == 1) {
                let res = await getCadreCheckRedisBoolean(obj);

                if (res.data.code == 0 && res.data.data.status == 2) {
                    let progressres = await cadreCheck();
                    if (progressres.data.code == 0 && progressres.data.data) {
                        await getCadreCheckRedisBoolean(obj);
                    }
                }
                if (res.data.code == 0 && res.data.data.status == 0) {
                    progressnum = res.data.data.percent;
                    progresstotlenum = res.data.data.count
                    newprogresstotlenum = res.data.data.complete
                    setProgress(progressnum);
                }
                if (res.data.code == 0 && res.data.data.status == 1) {
                    progressnum = 100;
                    progresstotlenum = res.data.data.count
                    newprogresstotlenum = res.data.data.complete
                    setProgress(progressnum);
                    setTableData(res.data.data.list)
                    setTotalData(res.data.data.total)
                    setProgresstotle(res.data.data.count)
                    setnewProgresstotle(res.data.data.complete)
                    setTotalq(res.data.data.total)
                    setUsernum(res.data.data.user_number)
                    setBgstatus(true)
                    setLoading(false)
                    setUuiddata(res.data.data.uuid)
                }
            }
            if (menuKey == 2) {
                let res = await getOrgRedisBoolean();

                if (res.data.code == 0 && res.data.data && res.data.data.status == 2) {
                    let progressres = await orgAnalysisList();
                    if (progressres.data.code == 0 && progressres.data.data) {
                        await getOrgRedisBoolean();
                    }
                }
                if (res.data.code == 0 && res.data.data && res.data.data.status == 0) {
                    progressnum = res.data.data.percent;
                    progresstotlenum = res.data.data.count
                    newprogresstotlenum = res.data.data.complete
                    setProgress(progressnum);
                }
                if (res.data.code == 0 && res.data.data && res.data.data.status == 1) {
                    progressnum = 100;
                    progresstotlenum = res.data.data.count
                    newprogresstotlenum = res.data.data.complete
                    setProgresstotle(res.data.data.count)
                    setnewProgresstotle(res.data.data.complete)
                    setUsernum(res.data.data.user_number)
                    setTimedata(res.data.data.time)
                    setBgstatus(true)
                    setProgress((data) => {
                        return progressnum;
                    });
                }
            }
            if (menuKey == 3) {
                let res = await getRedisBoolean();

                if (res.data.code == 0 && res.data.data.status == 2) {
                    let progressres = await cadreAnalysisList();
                    if (progressres.data.code == 0 && progressres.data.data) {
                        await getRedisBoolean();
                    }
                }
                if (res.data.code == 0 && res.data.data.status == 0) {
                    progressnum = res.data.data.percent;
                    progresstotlenum = res.data.data.count
                    newprogresstotlenum = res.data.data.complete
                    setProgress(progressnum);
                }
                if (res.data.code == 0 && res.data.data.status == 1) {
                    progressnum = 100;
                    progresstotlenum = res.data.data.count
                    newprogresstotlenum = res.data.data.complete
                    setProgresstotle(res.data.data.count)
                    setnewProgresstotle(res.data.data.complete)
                    setUsernum(res.data.data.user_number)
                    setTimedata(res.data.data.time)
                    setBgstatus(true)
                    setProgress((data) => {
                        return progressnum;
                    });
                }
            }
            if (menuKey == 4) {
                let res = await mockMaterialsDownloadList()
                if (res.data.code == 0) {
                    res.data.data && res.data.data.length > 0 && res.data.data.map((item) => {
                        item.children && item.children.length > 0 && item.children.map((item1) => {
                            item1.selected = false
                            item1.fatId = item.mock_id
                            item1.fatname = item.mock_name,
                                item1.islodaing = false
                        })
                    })
                    setReportList(res.data.data);
                }
            }
            let title = `${progressnum}%`;
            let option = {
                title: {
                    text: title,
                    x: "center",
                    y: "40%",
                    textStyle: {
                        color: "#333",
                        fontSize: 12,
                        fontWeight: "bold",
                    },
                },
                series: [
                    {
                        type: "gauge",
                        radius: "100%",
                        clockwise: true,
                        // 修改 startAngle 为 90 度
                        startAngle: 90,
                        // 按顺时针方向递减角度
                        endAngle: -270,
                        splitNumber: 20,
                        detail: {
                            offsetCenter: [0, -20],
                            formatter: " ",
                        },
                        pointer: {
                            show: false,
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: [
                                    [0, "#1FA1FD"],
                                    [
                                        progressnum / 100,
                                        new echarts.graphic.RadialGradient(
                                            1,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "#1FA1FD",
                                                },
                                                {
                                                    offset: 0.5,
                                                    color: "#F2F2F2",
                                                },
                                                {
                                                    offset: 1,
                                                    color: "#1FA1FD",
                                                },
                                            ],
                                            true
                                        ),
                                    ],
                                    [1, "#F2F2F2"],
                                ],
                                width: 35,
                            },
                        },
                        axisTick: {
                            show: false,
                        },
                        splitLine: {
                            show: true,
                            length: 140,
                            lineStyle: {
                                color: "#fff",
                                width: 7,
                            },
                        },
                        axisLabel: {
                            show: false,
                        },
                    },
                    {
                        type: "pie",
                        name: "失败：",
                        radius: [0, "50.5%"],
                        data: [3],
                        hoverAnimation: false,
                        clockWise: false,
                        itemStyle: {
                            normal: {
                                color: "#fff",
                                shadowColor: "#58B6F8",
                                shadowBlur: 5,
                                shadowOffsetX: 0,
                                shadowOffsetY: 0,
                            },
                        },
                        label: {
                            normal: {
                                show: true,
                                position: "center",
                                padding: [20, 0, 0, 0],
                                align: "center",
                                fontSize: 10,
                                formatter: function (param) {
                                    return `${newprogresstotlenum}/${progresstotlenum}`;
                                },
                                rich: {
                                    a: {
                                        fontSize: 10,
                                        color: "#333333",
                                    },
                                    b: {
                                        fontSize: 10,
                                        color: "#333333",
                                    },
                                },
                            },
                        },
                    },
                ],
            };

            if (progressnum != newprogress && newprogress != 100) {
                setOption(option);
                setNewprogress(progressnum);
            }
        } catch (error) {
            console.error("获取进度数据时出错:", error);
        }
    };
    useEffect(() => {
        let timer;
        if (menuKey != 4) {
            if (progress != 100) {
                timer = setInterval(() => {
                    getprogressData(1);
                }, 5000);
            } else {
                if (menuKey == "3" && bgstatus) {
                    setReload(true)
                }
                clearInterval(timer);
            }
        }

        // 清理定时器，避免内存泄漏
        return () => clearTimeout(timer);
    }, [progress]);
    useEffect(() => {
        getprogressData(1);
    }, []);
    // 更新 去处理
    const updateProgress = async (record, type) => {
        try {
            // 设置加载状态
            setLoading(true);
            // 调用更新接口
            const res = await updateByCadreCheckId({ cadre_check_id: record.cadre_check_id });
            // 检查接口返回的状态码
            if (res.data.code === 0) {
                if (type === 2) {
                    // 如果 type 为 2，刷新进度数据
                    getProgressData(1);
                } else {
                    // 如果 type 不为 2，跳转到干部信息页面
                    const { org_id, count } = res.data.data;
                    // 提取必要的参数
                    const userId = record.user_id;
                    const selOrgId = org_id; // 默认值

                    // 确保所有必要的参数都存在
                    if (userId && org_id && selOrgId) {
                        // 构造跳转路径
                        const queryParams = new URLSearchParams({
                            user_id: userId,
                            sel_org_id: selOrgId,
                            org_id: org_id,
                            numper: count
                        }).toString();
                        // 跳转到新页面
                        history.push(`/cadre-information-maintenance?${queryParams}`);
                        handleCancel();
                    }
                }
            } else {
                console.error("接口返回错误代码:", res.data.code);
            }
        } catch (error) {
            // 捕获异常并打印错误信息
            console.error("更新数据时出错:", error);
        } finally {
            // 无论成功或失败，都清除加载状态
            setLoading(false);
        }
    };
    //重新核验
    const handleRetry = async () => {
        try {
            setProgress(0)
            setNewprogress(0)
            setReload(false)
            setOption({
                title: {
                    text: "0%",
                    x: "center",
                    y: "40%",
                    textStyle: {
                        color: "#333",
                        fontSize: 12,
                        fontWeight: "bold",
                    },
                },
                series: [
                    {
                        type: "gauge",
                        radius: "100%",
                        clockwise: true,
                        // 修改 startAngle 为 90 度
                        startAngle: 90,
                        // 按顺时针方向递减角度
                        endAngle: -270,
                        splitNumber: 20,
                        detail: {
                            offsetCenter: [0, -20],
                            formatter: " ",
                        },
                        pointer: {
                            show: false,
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: [
                                    [0, "#1FA1FD"],
                                    [
                                        0 / 100,
                                        new echarts.graphic.RadialGradient(
                                            1,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "#1FA1FD",
                                                },
                                                {
                                                    offset: 0.5,
                                                    color: "#F2F2F2",
                                                },
                                                {
                                                    offset: 1,
                                                    color: "#1FA1FD",
                                                },
                                            ],
                                            true
                                        ),
                                    ],
                                    [1, "#F2F2F2"],
                                ],
                                width: 35,
                            },
                        },
                        axisTick: {
                            show: false,
                        },
                        splitLine: {
                            show: true,
                            length: 140,
                            lineStyle: {
                                color: "#fff",
                                width: 7,
                            },
                        },
                        axisLabel: {
                            show: false,
                        },
                    },
                    {
                        type: "pie",
                        name: "失败：",
                        radius: [0, "50.5%"],
                        data: [3],
                        hoverAnimation: false,
                        clockWise: false,
                        itemStyle: {
                            normal: {
                                color: "#fff",
                                shadowColor: "#58B6F8",
                                shadowBlur: 5,
                                shadowOffsetX: 0,
                                shadowOffsetY: 0,
                            },
                        },
                        label: {
                            normal: {
                                show: true,
                                position: "center",
                                padding: [20, 0, 0, 0],
                                align: "center",
                                fontSize: 10,
                                formatter: function (param) {
                                    return "0/0";
                                },
                                rich: {
                                    a: {
                                        fontSize: 10,
                                        color: "#333333",
                                    },
                                    b: {
                                        fontSize: 10,
                                        color: "#333333",
                                    },
                                },
                            },
                        },
                    },
                ],
            })
            let res;
            if (menuKey == 1) {
                setLoading(true)

                res = await cadreCheck();
            }
            if (menuKey == 3) {
                res = await cadreAnalysisList();
            }
            if (menuKey == 2) {
                res = await orgAnalysisList();
            }
            if (res.data.code == 0) {
                setTimeout(() => {
                    getprogressData(1);
                }, 2000)
            }
        } catch (error) {
            console.error(error);
        }
    }
    // 选中方案方法
    const handleSelectOption = (item, option) => {
        const newreportList = [...reportList];
        let isOptionSelected = false;
        let newSelectedOption = [...selectedOption];
        newreportList.forEach((item) => {
            item.children.forEach((subitem) => {
                if (subitem.id === option.id) {
                    subitem.selected = !subitem.selected;
                    isOptionSelected = subitem.selected;
                }
            });
        });
        if (isOptionSelected) {
            newSelectedOption.push(option);
        } else {
            newSelectedOption = newSelectedOption.filter(
                (selected) => selected.id !== option.id
            );
        }
        setReportList(newreportList);
        setSelectedOption(newSelectedOption);
    };
    // 方案下载列表
    const [downloadList, setDownloadList] = useState([
        {
            name: "已生成《10月12日部务会讨论修订方案》研判名单",
            url: "",
        },
        {
            name: "已生成《10月12日部务会讨论修订方案》研判名单",
            url: "",
        },
        {
            name: "已生成《10月12日部务会讨论修订方案》研判名单",
            url: "",
        },
        {
            name: "方案四",
            url: "111",
        },
    ]);
    // 上传报告后
    const [showreport, setShowreport] = useState(false);
    // 取消选中
    const handleCancelOption = (condition) => {
        const newreportList = [...reportList];
        let newSelectedOption = [...selectedOption];

        newreportList.forEach((item) => {
            item.children.forEach((subitem) => {
                if (condition(subitem)) {
                    subitem.selected = false;
                    // 从选中列表中移除该选项
                    newSelectedOption = newSelectedOption.filter(
                        (selected) => selected.id !== subitem.id
                    );
                }
            });
        });

        setReportList(newreportList);
        setSelectedOption(newSelectedOption);
    };
    // 上传报告
    const handleUpload = async () => {
        if (selectedOption.length === 0) {
            message.error("请选择至少一份材料");
            return;
        }
        setBtnloading(true);
        setTimeout(() => {
            setShowreport(true);
            setGeneratereportstatus(false);
            setBtnloading(false)
        }, 2500)

    };
    // 下载
    const handleDownloaditem = async (item) => {
        // 如果已经在加载中，直接返回，防止重复点击
        if (item.islodaing) {
            return;
        }

        try {
            // 设置当前项为加载状态
            let newDownloadList = [...selectedOption];
            newDownloadList.forEach((i) => {
                if (i.id == item.id) {
                    i.islodaing = true;
                }
            });
            setSelectedOption(newDownloadList);

            // 调用下载接口
            let obj = {
                mock_id: item.fatId,
                material_type: item.material_type,
                type: 2
            };

            let res = await meetingMaterialsDownload(obj);
            console.log(res);

            // 无论成功还是失败，都要重置加载状态
        } catch (error) {
            console.error('下载失败:', error);
            // 可以在这里添加错误提示
            // message.error('下载失败，请重试');
        } finally {
            // 确保在任何情况下都重置加载状态
            let resetDownloadList = [...selectedOption];
            resetDownloadList.forEach((i) => {
                if (i.id == item.id) {
                    i.islodaing = false;
                }
            });
            setSelectedOption(resetDownloadList);
        }
    }
    const columns = [
        {
            title: "姓名",
            dataIndex: "user_name",
            key: "user_name",
            align: "center",
            width: 50,
        },
        {
            title: "现任职务",
            dataIndex: "current_job",
            key: "current_job",
            align: "left",
            ellipsis: true,
            width: 70,
        },
        {
            title: "错误信息",
            dataIndex: "check_info",
            key: "check_info",
            align: "left",
            ellipsis: true,
            width: 80,
        },
        {
            title: "操作",
            key: "action",
            align: "center",
            width: 60,
            render: (text, record) => (
                <span>
                    {
                        record.status == 0 ?
                            <span>
                                {
                                    record.check_type == 1 || record.check_type == 2 ?
                                        <Button
                                            type="link"
                                            onClick={() => {
                                                updateProgress(record, 1)
                                            }}
                                        >去处理</Button>
                                        :
                                        record.check_type == 3 || record.check_type == 4 ?
                                            <Button
                                                type="link"
                                                onClick={() => {
                                                    updateProgress(record, 2)
                                                }}
                                            >更新</Button>
                                            : ""
                                }
                            </span>
                            : <Button
                                type="link"
                                style={{ color: "#1BA200" }}
                            >已更新</Button>
                    }

                </span>
            ),
        },
    ];
    // 下载
    const handleDownload = async (url) => {
        try {
            exportbg({ uuid: uuiddata }).then((res) => {
                if (res.data.code === 0) {
                }
            });
        } catch (error) {
            console.error(error);
        }
    }
    return (
        <div className="content">
            {(menuKey == "1" || menuKey == "2" || menuKey == "3") && showreport == false ? (
                <div>
                    {!reload ? (
                        <div className={`progress ${menuKey == "1" ? "progress-responsive" : ""}`}>
                            <div className={`progress-bar ${menuKey == "1" && progress == 100 ? "progress-bar-responsive" : ""}`}>
                                <EChartsReact option={optionData} style={{ height: "172px" }} />
                            </div>
                            {progress >= 0 && progress < 100 ? (
                                <div className="text">
                                    {
                                        menuKey == "1" ? "干部资料核查中...，请等待！" :
                                            <span>
                                                正在生成
                                                {menuKey == "3" ? "干部" : "班子"}
                                                分析报告，请等待！
                                            </span>
                                    }

                                </div>
                            ) : menuKey == "1" && progress == 100 ? (
                                <div className="bar100">
                                    <div className="text_title">已完成核查！</div>
                                    <div className="text_sub">
                                        本次共核查
                                        <span className="text_totle">{usernum}</span>
                                        人,共发现问题
                                        <span className="text_totle" style={{ color: "red" }}>
                                            {totalq}
                                        </span>
                                        个！
                                    </div>
                                    <div className="tablecss">
                                        <div className="tablecss_top">
                                            <div className="tablecss_top_div"
                                                onClick={() => {
                                                    handleDownload()
                                                }}
                                            >
                                                <img src={download} />
                                                <span>下载</span>
                                            </div>

                                        </div>
                                        <Table
                                            loading={loading}
                                            scroll={{ y: 350 }}
                                            bordered
                                            columns={columns}
                                            dataSource={tableData}
                                            pagination={totalData > 0 ? {
                                                pageSize: 10,
                                                current: page,
                                                total: totalData,
                                                onChange: (page, pageSize) => {
                                                    setPage(page);
                                                    getprogressData(page);
                                                },
                                                showSizeChanger: false,
                                                showLessItems: true,
                                                hideOnSinglePage: true,
                                            } : false}
                                            className="hideScrollbar"
                                            style={{
                                                overflowY: "auto",
                                                WebkitOverflowScrolling: "touch",
                                            }}
                                            rowKey="cadre_check_id"
                                        />
                                    </div>
                                </div>
                            ) : progress == 100 ? (
                                <div className="bar100">
                                    <div className="text_title">生成完成！</div>
                                    <div className="text_sub">
                                        本次生成
                                        <span className="text_totle">{progresstotle}</span>
                                        份
                                        {menuKey == "3" ? "干部" : "班子"}
                                        分析报告
                                    </div>
                                </div>
                            ) : (
                                ""
                            )}
                        </div>
                    ) : (
                        <div className="tops">
                            <div className="imgbox">
                                <img src={tips} />
                            </div>
                            <div className="text_title">请确定是否重新进行生成？</div>
                            <div className="tips_content">
                                <div className="tips_content_texttop">
                                    上一次干部分析报告生成完成时间为
                                </div>
                                <div className="tips_content_time">{timedata}</div>
                                <div className="tips_content_bottom">
                                    上一次共生成 <span className="spantext">{progresstotle}</span>
                                    份干部分析报告
                                </div>
                            </div>
                        </div>
                    )}

                    <div style={{ marginBottom: menuKey == "1" ? '5px' : "20px" }} className="btn">
                        {progress == 100 || reload ? (
                            <Button className="reset" onClick={() => {
                                handleRetry()
                            }}>
                                {menuKey == "1" ? "重新核验" : "重新生成"}
                            </Button>
                        ) : (
                            ""
                        )}
                        {progress == 100 && menuKey == "1" ? (
                            ""
                        ) : (
                            <Button className="btnclose" onClick={handleCancel}>
                                关闭
                            </Button>
                        )}
                        {menuKey == "1" && progress != 100 && (
                            <div className="menukeyfooter">
                                温馨提示：加载需要较长时间，您可以退出界面，小丰同学将在后台自动完成（退出不影响加载进度）
                            </div>
                        )}
                    </div>
                </div>
            ) : menuKey == "4" && showreport == false ? (
                <div className="report">
                    <div className="report_title">选择需要生成的材料</div>
                    <div className="report_content">
                        {reportList.map((item, index) => (
                            <div className="report_content_top" key={index}>
                                <div className="report_content_top_title">{item.mock_name}</div>
                                <div className="report_content_item_content">
                                    {item.children.map((subitem, subindex) => (
                                        <div key={subindex}>
                                            <div
                                                onClick={() => handleSelectOption(item, subitem)}
                                                className={
                                                    subitem.selected
                                                        ? "report_content_top_tagactive"
                                                        : "report_content_top_tag"
                                                }
                                            >
                                                {subitem.name}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                    <div className="report_bottom">
                        <div className="report_bottom_title">已选择的材料</div>
                        <div className="report_bottom_content">
                            {selectedOption.map((item, index) => (
                                <div className="report_bottom_content_item" key={index}>
                                    <div className="report_bottom_content_text">{item.name}</div>
                                    <div
                                        className="report_bottom_content_close"
                                        onClick={() =>
                                            handleCancelOption(
                                                (subitem) => subitem.id === item.id
                                            )
                                        }
                                    >
                                        x
                                    </div>
                                </div>
                            ))}
                            <div className="uploadIcon" onClick={() => handleUpload()}>
                                {
                                    btnloading ? <Button type="primary" shape="circle" loading /> :
                                        <img src={UploadIcon} />
                                }
                            </div>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="reportlist">
                    {selectedOption.map((item, index) => (
                        <div className="reportlist_item" key={index}>
                            <div className="reportlist_item_title">已生成《{item.fatname}》{item.name}</div>
                            <div className="reportlist_item_content">
                                {!item.islodaing ? (
                                    <div
                                        className="reportlist_item_content_btn"
                                        onClick={() => handleDownloaditem(item)}
                                    >
                                        <img src={download} />
                                        <span>下载</span>
                                    </div>
                                ) : (
                                    <div className="lodaingIcon">
                                        {/* <Button type="primary" shape="circle" loading /> */}
                                        <span style={{ marginLeft: '8px', color: '#1FA1FD' }}>下载中...</span>
                                    </div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};
export default ModalAI;
